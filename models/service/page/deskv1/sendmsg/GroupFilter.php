<?php

class Service_Page_DeskV1_SendMsg_GroupFilter {

    private $courseId;
    private $taskId;
    private $lessonId;
    private $assistantUid;
    private $personUid;
    private $sendType;

    private $studentList;
    private $studentUidToWxIds;
    private $courseInfo;
    private $arrClassId2Name;

    private $arrTimeLine;
    private $mainGradeId;
    private $examType;
    private $strTips = '';
    private $arrBindWxStudentUid = [];
    private $arrBindWxStudentList = [];
    private $arrUnBindWxStudentList= [];

    private $sceneFilter;

    private $examInfo;
    private $answerList;

    private $objStudentSendWxInfo;
    private $objSceneConf;

    private $reportMap;

    //融合课中互动题总题数
    private $dimLpcLessonCommonWithLessonId = [];

    private $groupItem;
    private $sceneContext;
    private $output = [
                'tips' => '',
                'attendTimeline' => [],
                'unBindList' => [],
                'groupList' => [],
            ];

    const VALID_SEND_TYPE = [
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_7,
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_ATTEND_OVERVIEW,
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_ATTEND_OVERVIEW_PLAYBACK,
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_SPORT_WEEK_REPORT,
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_PRONUNCIATION_REPORT_GROUP,
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_VOICE_REPORT_GROUP,
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_DEER_CEPING_STAGE1_REPORT,
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_DEER_CEPING_STAGE2_REPORT,
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_PERSONAL_LEARN_FEEDBACK,
    ];


    const STATUS_OK = 0;         // 可发送
    const STATUS_DISABLED = 1;   //不可发送

    const STATUS_NEVER_SENT = 0;       // 未发过
    const STATUS_SENT = 1;             // 发过



    /**
     * 部分组合不放示例图
     *
     * @var array
     */
    private static $arrNoAttachmentGroup = [
        'notready_1' => 0,
    ];

    public function __construct() {
        $this->objStudentSendWxInfo      = new Service_Data_StudentSendWxInfo();
        $this->objSceneConf              = new Service_Data_Deskv1_SenceConf();
    }


    /**
     * 到课时间线
     *
     * @param int $intLessonId
     * @return null
     */
    private function getAttendTimeline() {
        $lessonData = $this->courseInfo;
        if (!$lessonData['lessonList']) {
            throw new Common_Exception(Common_ExceptionCodes::DESK_NETWORK_ERROR, '获取章节信息异常');
        }

        if (!isset($lessonData['lessonList'][$this->lessonId])
            || (0 >= $lessonData['lessonList'][$this->lessonId]['startTime'])
            || (0 >= $lessonData['lessonList'][$this->lessonId]['stopTime'])
        ) {
            throw new Common_Exception(Common_ExceptionCodes::DESK_NETWORK_ERROR, '章节信息有误');
        }
        $arrTmp = $lessonData['lessonList'][$this->lessonId];
        unset($lessonData['lessonList']);

        $this->arrTimeLine = [
            'start' => $arrTmp['startTime'] - 1800, // 开课前后30min
            'end' => $arrTmp['stopTime'] + 1800,
            'lessonStart' => $arrTmp['startTime'],
            'lessonEnd' => $arrTmp['stopTime'],
        ];
    }

    /**
     * 获取小班id以及名称
     * @throws Common_Exception
     */
    private function getClassInfo(){
        foreach ($this->studentList as $row){
            $this->arrClassId2Name[$row['classId']] = $row['groupName'];
        }
    }

    /**
     * 获取学生基本信息
     * @throws Common_Exception
     */
    private function getStudentBasicInfo() {
        $arrStudentUid = array_keys($this->studentList);
        if (!$arrStudentUid) {
            return;
        }
        //todo 获取学生已经发送的微信条数
        $studentUidCntList = $this->objStudentSendWxInfo->getStudentsSendWxInfoCnt($this->assistantUid, $arrStudentUid);
        if (false === $studentUidCntList) {
            throw new Common_Exception(Common_ExceptionCodes::DESK_NETWORK_ERROR,'获取学生发送微信条数数据异常');
        }

        // 按照姓名进行排序
        $this->studentList = Tools_Array::sortChineseByField($this->studentList, 'studentName');

        $studentWxInfo = AssistantDesk_KunpengEnterprise::getWxidByUid($this->assistantUid, $arrStudentUid, Assistant_Const_WeChat::WORK_APP_ID);
        if (false === $studentWxInfo) {
            throw new Common_Exception(Common_ExceptionCodes::DESK_API_ERROR, '根据学生UID获取微信ID信息异常!');
        }
        if (!$studentWxInfo || !is_array($studentWxInfo)) {
            throw new Common_Exception(Common_ExceptionCodes::DESK_TIPS, '未查询到符合条件的学生，请先将微信和学生id绑定之后再进行群发操作！');
        }
        $arrTmp = [];
        foreach ($studentWxInfo as $row) {
            $studentUid = $row['studentUid'];
            if (!isset($arrTmp[$studentUid])) {
                $arrTmp[$studentUid] = 0;
            }
            $arrTmp[$studentUid] += 1;
        }

        foreach ($this->studentList as $studentUid => $v) {
            $v['reason'] = '';
            $v['className'] = sprintf(
                '%s-%s',
                $this->courseInfo['courseName'],
                 $this->arrClassId2Name[$v['classId']] ?? ''
            );
            if ($arrTmp[$studentUid]) {
                $v['wxNum'] = $arrTmp[$studentUid];
                if ($studentUidCntList[$studentUid]['surplusNum'] <= Service_Data_StudentSendWxInfo::WX_INFO_DISABLE) {
                    $v['reason'] = Service_Data_StudentSendWxInfo::WX_INFO_LIMIT_ERR;
                    $this->arrUnBindWxStudentList[$studentUid] = $v;
                }else{
                    $this->arrBindWxStudentList[$studentUid] = $v;
                }
            } else {
                $v['reason'] = '未加微或未反确认';
                $this->arrUnBindWxStudentList[$studentUid] = $v;
            }
        }

        $this->arrBindWxStudentUid = array_keys($this->arrBindWxStudentList);
    }


    public function getReportInfo(){
        $arrStudentUid = array_keys($this->studentList);
        if (!$arrStudentUid) {
            return;
        }
        $fields = [
            "course_id",
            "week",
            "user_id",
            "rating",
            "report_id",
            "duration_detail",
        ];
        $reportList = Api_DataProxy::getSportWeekReportList($this->courseId,$this->taskId,$arrStudentUid,$fields);
        $reportMap = [];
        if ($reportList){
            foreach ($reportList as $report){
                if ($report['duration_detail'] && $report['duration_detail'] != "{}" && $report['report_id']) {
                    $reportMap[$report['user_id']] = $report;
                }
            }
        }
        $this->reportMap = $reportMap;

    }

    public function  formatReportOutput(){
        $this->output['variableNames'] = ["学生名","学科","服务老师真名","课程名称","运动周报告"];
        $this->output['unBindList'] = array_values($this->arrUnBindWxStudentList);
        $countNoPicture = count($this->output['unBindList']);
        if (!empty($this->arrBindWxStudentList)) {
            $resultList = [];
            foreach ($this->arrBindWxStudentList as $studentUid => $items){
                $report = $this->reportMap[$studentUid];
                if ($report) {
                    $resultList[$report['rating']][] = $studentUid;
                }else {
                    $resultList[Service_Data_Sport_SportWeekReport::UN_GENERATE][] = $studentUid;
                }
            }
            $groupList = [];
            $index = 0;
            $resultListV2 = [];
            foreach ($resultList as $key => $studentUidList) {
                $resultListV2[$key] = [
                    "sortVale"=>Service_Data_Sport_SportWeekReport::$RatingSortMap[Service_Data_Sport_SportWeekReport::$RatingMap[$key]],
                    "studentUidList" =>$studentUidList
                ];
            }

            uasort($resultListV2,function ($v1,$v2){
                return $v1['sortVale'] - $v2['sortVale'];
            });
            foreach ($resultListV2 as $key => $resultVal){
                $studentUidList = $resultVal['studentUidList'];
                $group['defaultTpl'] = [""];
                $group['bindList'] = $this->formatStudentList($studentUidList, $index++,1);


                //转成二进制排序
                $newKey =   Service_Data_Sport_SportWeekReport::$RatingMap[$key];


                if ($key == Service_Data_Sport_SportWeekReport::UN_GENERATE) {
                    $countNoPicture+=count($studentUidList);
                }

                $groupList[$newKey] = $group;

            }


            //$i = 1;
            foreach ($groupList as $key => $group){
                //$groupList[$key]['title']      = $key;
                $groupList[$key]['labelList'] = [$key];
                if ($key == Service_Data_Sport_SportWeekReport::UN_GENERATE_CONTENT) {
                    continue;
                }
                $groupList[$key]['attachment'][] = [
                    'msgType' => Api_Kunpeng::MSG_TYPE_IMAGE,
                    'msgContent' => [
                        'ori' => 'sport_week_sample.png',
                        'name' => 'zyb_e0e4e49424149a6d30b22e80a63f1ed8.jpg',
                        'url' => 'https://img.zuoyebang.cc/zyb_e0e4e49424149a6d30b22e80a63f1ed8.jpg',
                        'width' => 720,
                        'height' => 624,
                    ],
                    'sample' => 1,
                ];
            }


            $tips = sprintf(Service_Data_Deskv1_SenceConf::TIPS_REPORT, count($this->studentList), $countNoPicture);

            $this->output['groupList'] = array_values($groupList);;


            $this->output['tips'] = $tips;
        }

    }

    /**
     * answerList 换成 labelList
     * 预习点评没有tips 、attendTimeLine、attachment
     * 预习点评bindList 需要 gcid
     * 更换模板
     * 预习点评格式化数据
     */
    private function examByLabel() {
        //获取所有题目
        $tids = $this->examInfo['questionList'] ? array_keys($this->examInfo['questionList']) : [];
        if(empty($tids)) {
            throw new Common_Exception(Common_ExceptionCodes::DESK_NETWORK_ERROR, '获取预习题目异常');
        }

        //按照结果分组
        $resultList = [];
        foreach ($this->arrBindWxStudentList as $studentUid => $items){
            $answerKey = [];
            foreach ($tids as $tid){
                $answerKey[] = $this->answerList[$studentUid]['answerList'][$tid]['correct'] ? 1 : 0; //1代表正确,0否
            }
            $resultList[implode('_', $answerKey)][] = $studentUid;
        }

        $groupList = [];
        $index = 0;
        foreach ($resultList as $key => $studentUidList){
            $group['answerList'] = array_map("intval",explode('_', $key));
            $group['defaultTpl'] = $this->getTemplate($key);
            $group['bindList'] = $this->formatStudentList($studentUidList, $index++);
            //转成二进制排序
            $newKey =   bindec(join('', $group['answerList']));
            $groupList[$newKey] = $group;
        }
        ksort($groupList);

        $i = 1;
        foreach ($groupList as $key => $group){
            $groupList[$key]['title']      = "组合". $i++;
            foreach ($group['answerList'] as $idx => $answer){
                $text = 0 === $answer ? '错' : '对';
                $groupList[$key]['labelList'][] = "第".($idx+1)."题$text";
            }
            unset($groupList[$key]['answerList']);
        }
        return array_values($groupList);
    }

    private function groupFilterDeskGo() {
        $params = [
            'assistantUid' => $this->assistantUid ?? 0,
            'personUid' => $this->personUid ?? 0,
            'courseId' => $this->courseId ?? 0,
            'lessonId' => $this->lessonId ?? 0,
            'sendType' => $this->sendType ?? 0,
            'taskId' => $this->taskId ?? 0,
            'sceneContext' => $this->sceneContext ?? '',
            'arrBindWxStudentUids' => $this->arrBindWxStudentUid ?? [],
        ];
        $data = \Api_Assistantdeskgo_Api::groupFilterGroupBind($params);
        Bd_Log::notice("groupFilterDeskGo params: " . json_encode($params) . ', return data: ' . json_encode($data));

        $resultList = $data['resultList'];

        $groupList = [];
        $index = 0;
        foreach ($resultList as $resultItem){
            $group['title'] = $resultItem['title'] ?? '分组'. ($index + 1);
            $group['labelList'] = $resultItem['labelList'] ?? [];
            $group['defaultTpl'] = $resultItem['defaultTpl'] ?? [];
            $group['bindList'] = $this->formatStudentList($resultItem['studentUidList'], $index++) ?? [];
            $group['variableNames'] = $resultItem['variableNames'] ?? [];
            $groupList[] = $group;
        }

        return $groupList;
    }

    /**
     * 跟课详情反馈格式化数据
     * @param null
     * @return array|null
     */
    private function groupByLabel() {
        $arrRet = [];
        $arrGID2StudentList = [];
        $arrGID2Data = [];
        $arrGID2NlpId = [];
        $arrGID2Tpl = [];
        $arrLabelGroup = Service_Data_Deskv1_SenceConf::OVERVIEW_LABAL[$this->sendType] ?? [];

        foreach ($this->arrBindWxStudentList as $v) {
            $v['status'] = self::STATUS_OK;
            $strGroup = '';

            if (!is_null($v['preAttendLabel'])) {
                $strTmp = 'preattend_' . $v['preAttendLabel'];
                if (isset($arrLabelGroup[$strTmp])) {
                    $strGroup = $strTmp;
                }
            }

            if (empty($strGroup) && (is_null($v['attendLabel']) || $v['attendLabel']==0)){
                //未就绪标签逻辑调整
                //student_attend_label字段值如果为0或者null则判定为 「未到课」（和student_attend_label=1做一样的处理）
                $strGroup = 'attend_1';
            }

            // 存在课中标签单独组合的情况
            if (empty($strGroup) && !is_null($v['attendLabel'])) {
                $strTmp = 'attend_' . $v['attendLabel'];
                if (isset($arrLabelGroup[$strTmp])) {
                    $strGroup = $strTmp;
                }
            }
            if (empty($strGroup)) {
                if(intval($v['interactionLabel']) == 0) {
                    $v['interactionLabel'] = 1; //interactionLabel为0时默认为1，避免出现两个参与度低的标签
                }
                $strTmp .= '|interaction_' . intval($v['interactionLabel']);
                if (isset($arrLabelGroup[$strTmp])) {
                    $strGroup = $strTmp;
                }
            }
            if (empty($strGroup)) {
                // 数据未就绪，不让发送消息
                $strGroup = 'notready_1';
                $v['status'] = self::STATUS_DISABLED;
            }
            unset($v['preAttendLabel'], $v['attendLabel'], $v['interactionLabel']);

            if (!isset($arrRet[$strGroup])) {
                $arrGID2Data[$strGroup] = [
                    'title' => '',
                    'labelList' => [],
                    'bindList' => [],
                    'defaultTpl' => [],
                    'attachment' => [],
                ];

                // 不是未到课或请假，配一张假图
                if (!isset(self::$arrNoAttachmentGroup[$strGroup])) {
                    $arrGID2Data[$strGroup]['attachment'][] = [
                        'msgType' => Api_Kunpeng::MSG_TYPE_IMAGE,
                        'msgContent' => [
                            'ori' => 'attent_class_sample.png',
                            'name' => 'zyb_3459f88f65cb79a5c2772fd48ff271ce.png',
                            'url' => 'https://img.zuoyebang.cc/zyb_3459f88f65cb79a5c2772fd48ff271ce.png',
                            'width' => 720,
                            'height' => 624,
                        ],
                        'sample' => 1,
                    ];
                }
            }

            $arrGID2StudentList[$strGroup][] = $v;
        }
        // 提示文案
        $intTotal = count($this->studentList);
        if (isset($arrGID2StudentList['notready_1'])) {

            $this->strTips = sprintf(Service_Data_Deskv1_SenceConf::TIPS_WITH_NOTREADY, $intTotal, count($arrGID2StudentList['notready_1']));
        } else {
            $this->strTips = sprintf(Service_Data_Deskv1_SenceConf::TIPS_NORMAL, $intTotal);
        }

        // 群发文案
        foreach ($arrGID2Data as $group => $v) {
            $arrTmp = explode('|', $group);
            foreach ($arrTmp as $tmp) {
                list($class, $id) = explode('_', $tmp);
                if (isset(Service_Data_Deskv1_SenceConf::LABEL_ARRAY[$class]) && isset(Service_Data_Deskv1_SenceConf::LABEL_ARRAY[$class][$id])) {
                    if ($this->sendType == Assistant_Ds_WxMessageSendRecord::SEND_TYPE_ATTEND_OVERVIEW_PLAYBACK && Service_Data_Deskv1_SenceConf::LABEL_ARRAY[$class][$id]['name'] == '未到课') {
                        $arrGID2Data[$group]['labelList'][] = '未观看';
                    } else {
                        $arrGID2Data[$group]['labelList'][] = Service_Data_Deskv1_SenceConf::LABEL_ARRAY[$class][$id]['name'];
                    }

                    if (isset(Service_Data_Deskv1_SenceConf::LABEL_ARRAY[$class][$id]['nlpid'])) {
                        $arrGID2NlpId[$group][] = Service_Data_Deskv1_SenceConf::LABEL_ARRAY[$class][$id]['nlpid'];
                    }
                }
            }
        }
        if ($arrGID2NlpId) {
            $mixedRet = Api_Nlp_ClassFollowComment::getTemplate($arrGID2NlpId, $this->courseInfo['mainGradeId']);
            if ($mixedRet) {
                $arrGID2Tpl = $mixedRet;
            }
        }

        // 按小班分组
        $arrGroupByClassId = [];
        foreach ($arrGID2StudentList as $group => $studentList) {
            $arrTmp = [];

            foreach ($studentList as $student) {
                $classId = $student['classId'];
                if (!isset($arrTmp[$classId])) {
                    $arrTmp[$classId] = [
                        'classId' => $classId,
                        'className' => isset($this->arrClassId2Name[$classId]) ? $this->arrClassId2Name[$classId] : '',
                        'studentList' => [],
                    ];
                }

                $arrTmp[$classId]['studentList'][] = $student;
            }
            $arrGroupByClassId[$group] = $arrTmp;
        }
        unset($arrGID2StudentList);

        $intTmp = 0;
        foreach ($arrLabelGroup as $group => $v) {
            if (isset($arrGID2Data[$group])) {
                $arrGID2Data[$group]['title'] = sprintf('组合%s', Service_Data_Deskv1_SenceConf::LABEL_NAME_SUFFIX[$intTmp]);
                if (isset($arrGID2Tpl[$group])) {
                    $arrGID2Data[$group]['defaultTpl'] = AssistantDesk_MessageTemplate::splitTplToArray($arrGID2Tpl[$group]);
                }

                foreach ($this->arrClassId2Name as $classId => $name) {
                    if (isset($arrGroupByClassId[$group][$classId])) {
                        // 方便前端标识数据 组小班id
                        $arrGroupByClassId[$group][$classId]['gcid'] = sprintf('idx_%s_%s', $intTmp, $classId);
                        $arrGID2Data[$group]['bindList'][] = $arrGroupByClassId[$group][$classId];
                    }
                }
                $arrRet[] = $arrGID2Data[$group];
                unset($arrGID2Data[$group]);
                $intTmp += 1;
            }
        }

        return $arrRet;
    }

    /**
     *
     *
     * @param null
     * @return null
     */
    private function getStudentLessonData() {
        if (!$this->arrBindWxStudentUid) {
            Bd_Log::notice("场景化群发 {$this->sendType} 未获取到绑定微信到学员数据");
            return true;
        }

        // 预到课 已请假的学生列表
        $arrConds = [
            'assistantUid' => $this->assistantUid,
            'lessonId' => $this->lessonId,
            'preAttend' => Service_Data_LessonStudent::PRE_ATTEND_LEAVE,
            'status' => Service_Data_LessonStudent::STATUS_OK,
        ];
        $ret = (new Service_Data_LessonStudent())->getListByConds($this->courseId, $arrConds, ['studentUid']);
        if (false === $ret) {
            throw new Common_Exception(Common_ExceptionCodes::DESK_DB_SELECT_FAILED, '获取学生预到课数据失败');
        }
        $arrPreAttendLeave = [];
        if (!empty($ret)) {
            foreach ($ret as $row) {
                $arrPreAttendLeave[$row['studentUid']] = 0;
            }
        }


        // 互动题数据。浣熊的课程直接查es，一课走das。2020暑可都从es lu表查询
        $arrFields = [
            'studentUid',
            'tradeStatus',
            'attendOverview',
            'attendLabel',
            'interactionLabel',
            'exerciseRightNum',
            'exerciseParticipateNum',
            //'playbackAttendLabel',
            'inclass_teacher_room_total_playback_time_v1',
            'inclass_teacher_room_attend_duration',
            'is_inclass_teacher_room_view_finish_total_playback_three_five_v1',
            'playbackInteractionLabel',
            'playbackTotalTime',
            'attendLong',
            'playbackParticipateNum',
            'playbackRightNum',
            //融合直播间数据
            'mix_live_interaction_right_num',
            'mix_live_interaction_submit_num',
            'mix_playback_interaction_right_num',
            'mix_playback_interaction_submit_num',
            //新时长
            'inclass_teacher_room_total_playback_time_v1',

        ];
        //互动题总数
        $interactInfo = AssistantDesk_ExamBind::getInteractTotalNum([$this->lessonId]);
        $intTotalNum  = $interactInfo[$this->lessonId] ?? 0;

        $mixedRet = \Assistant_Common_Service_DataService_Query_LessonStudentData::getListByCourseIdsLessonIdsStudentUids([$this->courseId], [$this->lessonId], $this->arrBindWxStudentUid, $arrFields);

        if (false === $mixedRet) {
            throw new Common_Exception(Common_ExceptionCodes::DESK_NETWORK_ERROR, '获取学生互动题答题数据异常');
        }

        $mixedRet = Tools_Array::getNewKeyArray($mixedRet, 'studentUid');

        //公共数仓lu数据
        $commonLuData = Api_DataProxy::getCommonLuByLessonStudents($this->lessonId, $this->arrBindWxStudentUid, ['student_uid', 'inclass_teacher_room_total_playback_time_v1', 'inclass_teacher_room_attend_duration', 'is_inclass_teacher_room_view_finish_total_playback_three_five_v1']);
        if (empty($commonLuData)) {
            $commonLuData = [];
        }
        $commonLuData = Tools_Array::getNewKeyArray($commonLuData, 'student_uid');


        // 课中接口，暂时不用。
        //$arrInclassReportData = Api_Jx::getLessonReportExercise($this->arrBindWxStudentUid, $this->lessonId) ?? [];
        foreach ($this->arrBindWxStudentUid as $studentUid) {
            $arrTmp = [
                'interactDetail' => [],
                'attendDetail' => [],
                'preAttendLabel' => null,
                'playbackTime' => 0,
                'attendLabel' => null,
                'interactionLabel' => null,
                'isAttend' => 0,
            ];

            if (isset($commonLuData[$studentUid])) {
                // 直播到课时长
                $arrTmp['attendDuration'] = $commonLuData[$studentUid]['inclass_teacher_room_attend_duration'] ?? 0;
                // 观看回放时长新
                $arrTmp['playbackDuration'] = $commonLuData[$studentUid]['inclass_teacher_room_total_playback_time_v1'] ?? 0;
            }

            if (isset($mixedRet[$studentUid]) && (1 === $mixedRet[$studentUid]['tradeStatus'])) {
                $row = $mixedRet[$studentUid];


                $row['exerciseRightNum']           = $row['exerciseRightNum'] ?? 0;
                $row['exerciseParticipateNum']     = $row['exerciseParticipateNum'] ?? 0;
                $arrTmp['interactDetail']['right'] = $row['exerciseRightNum'] ?? 0;
                // 错 = 参与 - 对
                $arrTmp['interactDetail']['wrong']      = max(0, ($row['exerciseParticipateNum'] - $row['exerciseRightNum']));
                $arrTmp['interactDetail']['unanswered'] = max(0, ($intTotalNum - $row['exerciseParticipateNum']));
                $arrTmp['isAttend']                     = (isset($mixedRet[$studentUid]['attendLong']) && (1 === $mixedRet[$studentUid]['attendLong'])) ? 1 : 0;
                if (Assistant_Ds_WxMessageSendRecord::SEND_TYPE_ATTEND_OVERVIEW_PLAYBACK === $this->sendType) {
                    // 回放标签 对答总 处理
                    $row['attendLabel'] = AssistantDesk_Data_PlayBackAttendLabel::getPlayBackAttendLabel(
                        $commonLuData[$studentUid]['inclass_teacher_room_total_playback_time_v1'] ?? 0,
                        $commonLuData[$studentUid]['inclass_teacher_room_attend_duration'] ?? 0,
                        $commonLuData[$studentUid]['is_inclass_teacher_room_view_finish_total_playback_three_five_v1'] ?? 0
                    );
                    !empty($row['playbackInteractionLabel']) && $row['interactionLabel'] = $row['playbackInteractionLabel'];
                    $correctCount    = $row['playbackRightNum'] + $row['exerciseRightNum'];
                    $partcipateCount = $row['playbackParticipateNum'] + $row['exerciseParticipateNum'];

                    $arrTmp['interactDetail']['right'] = $correctCount;
                    // 错 = 参与 - 对
                    $arrTmp['interactDetail']['wrong']      = max(0, ($partcipateCount - $correctCount));
                    $arrTmp['interactDetail']['unanswered'] = max(0, ($intTotalNum - $partcipateCount));
                }

                if (Zb_Const_GradeSubject::$GRADEMAPXB[$this->mainGradeId] == Zb_Const_GradeSubject::GRADE_STAGE_PRIMARY) {
                    //小学
                    $arrTmp['playbackTime'] = $row['playbackTotalTime'] ?? 0;
                } else {
                    //初高中
                    $arrTmp['playbackTime'] = $row['inclass_teacher_room_total_playback_time_v1'] ?? 0;
                }
                if (Zb_Const_GradeSubject::$GRADEMAPXB[$this->mainGradeId] == Zb_Const_GradeSubject::GRADE_STAGE_PRIMARY) {
                    //小学
                    $arrTmp['playbackTime'] = $row['playbackTotalTime'] ?? 0;
                } else {
                    //初高中
                    $arrTmp['playbackTime'] = $row['inclass_teacher_room_total_playback_time_v1'] ?? 0;
                }

                // 上课表现概述
                if ($row['attendOverview']) {

                    //过滤掉非法数据
                    $validAttendStatusArr = AssistantDesk_Inclass::attendTimeLineFilter($row['attendOverview'], true);

                    foreach ($validAttendStatusArr as $i => $v) {
                        if (isset($v['start_time']) && isset($v['end_time']) && isset($v['status_type'])) {
                            $sTime = $v['start_time'];
                            $eTime = $v['end_time'];

                            //因为去掉了某些非法时间段，因此可能造成时间段不连续。为确保时间段连续，使用下一个时间段开始时间作为当前时间段结束时间
                            if (isset($validAttendStatusArr[$i+1]) && $validAttendStatusArr[$i+1]['start_time']) {
                                $eTime = $validAttendStatusArr[$i+1]['start_time'];
                            }

                            // 时间范围限制
                            if (($sTime <= $eTime)
                                && ($sTime <= $this->arrTimeLine['end'])
                                && ($eTime >= $this->arrTimeLine['start'])
                            ) {
                                $arrTmp['attendDetail'][] = [
                                    'startTime' => max($sTime, $this->arrTimeLine['start']),
                                    'endTime' => min($eTime, $this->arrTimeLine['end']),
                                    'status' => $v['status_type'],
                                ];
                            }
                        }
                    }
                    $arrTmp['attendDetail'] = Tools_Array::sortByMultiCols($arrTmp['attendDetail'], ['startTime' => SORT_ASC]);
                }

                if (!empty($row['attendLabel']) && is_array($row['attendLabel'])) {
                    // 有关注度数据
                    $row['attendLabel'] = intval(array_pop($row['attendLabel']));
                    $arrTmp['attendLabel'] = $row['attendLabel'];

                    // 未就绪 转成 未到课
                    if (is_null($row['attendLabel']) || 0 == $row['attendLabel']) {
                        $arrTmp['attendLabel'] = Service_Data_Deskv1_SenceConf::LABEL_ATTEND_L1;
                    }
                } else {
                    // 没有关注度数据 转成 未到课
                    $arrTmp['attendLabel'] = Service_Data_Deskv1_SenceConf::LABEL_ATTEND_L1;
                }

                // 未到课 这里为了规避鲲鹏群发1v1消息结构强限制，需要加一张图，到课的有图
                if (Service_Data_Deskv1_SenceConf::LABEL_ATTEND_L1 === $arrTmp['attendLabel']) {
                    if (isset($arrPreAttendLeave[$studentUid])) {
                        // 未到课且已请假 转成 已请假
                        $arrTmp['preAttendLabel'] = Service_Data_Deskv1_SenceConf::LABEL_PREATTEND_LEAVE;
                        $arrTmp['attendSummary'] = [
                            'ori' => 'not_attend_but_leave.png',
                            'name' => 'zyb_5ca10673e59288b06849999f425f6c95.png',
                            'url' => 'https://img.zuoyebang.cc/zyb_5ca10673e59288b06849999f425f6c95.png',
                            'width' => 720,
                            'height' => 624,
                        ];
                    } else {
                        // 未到课
                        $arrTmp['attendSummary'] = [
                            'ori' => 'not_attend.png',
                            'name' => 'zyb_e5da65a874101935b601532957480be6.png',
                            'url' => 'https://img.zuoyebang.cc/zyb_e5da65a874101935b601532957480be6.png',
                            'width' => 720,
                            'height' => 624,
                        ];
                    }
                }

                if (!empty($row['interactionLabel']) && is_array($row['interactionLabel'])) {
                    $arrTmp['interactionLabel'] = intval(array_pop($row['interactionLabel']));
                }
            }

            $this->arrBindWxStudentList[$studentUid] = array_merge($this->arrBindWxStudentList[$studentUid], $arrTmp);
        }
    }

    /**
     * 获取已上传的上课表现图片
     *
     * @param null
     * @return null
     */
    private function getAttendSummaryImg() {
        if (empty($this->arrBindWxStudentUid)) {
            return;
        }
        $arrConds  = [
            'courseId' => $this->courseId,
            'lessonId' => $this->lessonId,
            'sendType' => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_ATTEND_OVERVIEW,
            'msgType' => Assistant_Ds_WechatAttachment::MSG_TYPE_IMAGE,
            sprintf('student_uid in (%s)', implode(',', $this->arrBindWxStudentUid)),
            'status' => Assistant_Ds_WechatAttachment::STATUS_OK,
        ];
        $arrFields = ['studentUid', 'content'];

        $mixedRet = [];
        if (Assistant_Ds_WxMessageSendRecord::SEND_TYPE_ATTEND_OVERVIEW_PLAYBACK !== $this->sendType) {
            // 回放反馈图片每次去最新的，不能用历史图片
            $mixedRet = (new Assistant_Ds_WechatAttachment())->getList($arrConds, $arrFields);
            if (false === $mixedRet) {
                throw new Common_Exception(Common_ExceptionCodes::DESK_DB_SELECT_FAILED, '查询已上传的学生上课表现图片失败');
            }
            $mixedRet = Tools_Array::getNewKeyArray($mixedRet, 'studentUid');
        }

        foreach($this->arrBindWxStudentUid as $studentUid) {
            if (!isset($this->arrBindWxStudentList[$studentUid]['attendSummary'])) {
                if (isset($mixedRet[$studentUid])) {
                    $this->arrBindWxStudentList[$studentUid]['attendSummary'] = $mixedRet[$studentUid]['content'];
                } else {
                    $this->arrBindWxStudentList[$studentUid]['attendSummary'] = null;
                }
            }
        }
    }


    private function formatOutput(){

        switch ($this->sendType) {
            case Assistant_Ds_WxMessageSendRecord::SEND_TYPE_7:
                if (!empty($this->arrBindWxStudentList)) {
                    $this->output['groupList'] = $this->examByLabel();
                }
                $this->output['unBindList'] = array_values($this->arrUnBindWxStudentList);
                $this->output['variableNames'] = ['学生名'];
                break;
            case Assistant_Ds_WxMessageSendRecord::SEND_TYPE_ATTEND_OVERVIEW:
            case Assistant_Ds_WxMessageSendRecord::SEND_TYPE_ATTEND_OVERVIEW_PLAYBACK:
                if (!empty($this->arrBindWxStudentList)) {
                    $this->output['groupList'] = $this->groupByLabel();
                }
                $this->output['attendTimeline'] = $this->arrTimeLine;
                $this->output['tips'] = $this->strTips;
                $this->output['unBindList'] = array_values($this->arrUnBindWxStudentList);
                $this->output['variableNames'] = ['学生名', '学科', '服务老师真名', '章节名称', '上课时间'];
                break;
            case Assistant_Ds_WxMessageSendRecord::SEND_TYPE_PERSONAL_LEARN_FEEDBACK:
                if (!empty($this->arrBindWxStudentList)) {
                    $this->output['groupList'] = $this->groupFilterDeskGo();
                }
                $this->output['unBindList'] = array_values($this->arrUnBindWxStudentList);
                $this->output['variableNames'] = ['学生名', '主讲老师名称', '阶段测题目总数', '阶段测正确题目数', '阶段测分数', '阶段测报告链接'];
                break;
            default:
                throw new  Common_Exception(Common_ExceptionCodes::DESK_PARAM_ERROR, '暂不支持该发送场景');
        }

    }

    private function initParam($arrInput) {
        if (0 >= $arrInput['assistantUid']) {
            throw new Common_Exception(Common_ExceptionCodes::DESK_PARAM_ERROR, '参数错误 未获取业务账号');
        }
        $this->assistantUid = $arrInput['assistantUid'];

        if (0 >= $arrInput['personUid']) {
            throw new Common_Exception(Common_ExceptionCodes::DESK_PARAM_ERROR, '参数错误 未获取真人账号');
        }
        $this->personUid = $arrInput['personUid'];

        if (0 >= $arrInput['courseId']) {
            throw new Common_Exception(Common_ExceptionCodes::DESK_PARAM_ERROR, '参数错误 未获取课程Id');
        }
        $this->courseId = $arrInput['courseId'];

        if (0 >= $arrInput['lessonId']) {
            throw new Common_Exception(Common_ExceptionCodes::DESK_PARAM_ERROR, '参数错误 未获取章节Id');
        }
        $this->lessonId = $arrInput['lessonId'];

        if (0 >= $arrInput['sendType']) {
            throw new Common_Exception(Common_ExceptionCodes::DESK_PARAM_ERROR, '参数错误 未获取场景类型');
        }
        $this->sendType = $arrInput['sendType'];

        if (!in_array($this->sendType, self::VALID_SEND_TYPE)) {
            throw new Common_Exception(Common_ExceptionCodes::DESK_PARAM_ERROR, '参数错误 不支持该场景类型');
        }
        if ($arrInput['taskId'] > 0 ) {
            $this->taskId = $arrInput['taskId'];
        }

        //初始化筛选统一数据
        AssistantDesk_Data_CommonParams::initParams($arrInput);
        $this->courseInfo = Common_Singleton::getInstanceData(AssistantDesk_Course::class, "getCourseInfo", [AssistantDesk_Data_CommonParams::$courseId]);
        $this->mainGradeId = Zb_Const_GradeSubject::$GRADEMAPXB[$this->courseInfo['mainGradeId']] ?? 0;
        //$this->initDimLpcLessonCommon();
        $this->groupItem = $arrInput['groupItem'];
        //场景化群发自定义筛选项（不走方舟）
        $sceneFilterData = json_decode($arrInput['sceneFilter'], true) ?: [];
        $this->sceneFilter = isset($sceneFilterData['scene']) ? $sceneFilterData['scene'] : [];
        $this->sceneContext = $arrInput['sceneContext'];
    }



    /**
     * 伴学、跟课详情反馈
     */
    private function getFeedback() {
        $this->getAttendTimeline();
        $this->getClassInfo();
        $this->getStudentBasicInfo();
        $this->getStudentLessonData();
        $this->getAttendSummaryImg();
        $this->formatOutput();
    }

    private function groupDeskGo() {
        $this->getClassInfo();
        $this->getStudentBasicInfo();
        $this->formatOutput();
    }

    /**
     * 确定学生是否需要使用自定义名称替换备注
     */
    private function checkStudentNameLabels() {
        if (empty($this->studentList)) {
            return;
        }

        $studentInfos = AssistantDesk_Student::getStudentInfo(array_keys($this->studentList));
        foreach ($this->studentList as $studentUid => &$studentInfo) {
            if (empty($studentInfos[$studentUid])) {
                $studentInfo['useLabel'] = false;
                continue;
            }
            //学生姓名
            $studentName = $studentInfos[$studentUid]['studentName'] ? $studentInfos[$studentUid]['studentName'] : '';
            //昵称
            $nickName = $studentInfos[$studentUid]['uname'] ? $studentInfos[$studentUid]['uname'] : '';

            $studentInfo['useLabel'] = Service_Data_User_UserBindConfig::checkStudentUseNameLabels($studentName, $nickName);
        }
    }

    private function buildStudentWxInfo() {
        if (empty($this->studentList)) {
            return;
        }

        $this->studentUidToWxIds = (new Service_Data_StudentSendWxInfo())->getStudentUidToWxIds($this->courseId, $this->assistantUid, array_keys($this->studentList));
        foreach ($this->studentList as $studentUid => &$studentInfo) {
            $studentInfo['wxIdsList'] = $this->studentUidToWxIds[$studentUid] ?? [];
        }
        Bd_Log::notice(sprintf("[buildStudentWxInfo], 学生信息: %s, 微信: %s", json_encode($this->studentList), json_encode($this->studentUidToWxIds)));
    }


    /**
     * 根据场景获取数据
     * @throws Common_Exception
     */
    private function sceneFilter() {
        switch ($this->sendType) {
            case Assistant_Ds_WxMessageSendRecord::SEND_TYPE_7:
                $this->getPreview();
                break;

            case Assistant_Ds_WxMessageSendRecord::SEND_TYPE_ATTEND_OVERVIEW:
            case Assistant_Ds_WxMessageSendRecord::SEND_TYPE_ATTEND_OVERVIEW_PLAYBACK:
                $this->getFeedback();
                break;
            case Assistant_Ds_WxMessageSendRecord::SEND_TYPE_SPORT_WEEK_REPORT:
                $this->getReport();
                break;
            case Assistant_Ds_WxMessageSendRecord::SEND_TYPE_PRONUNCIATION_REPORT_GROUP:
            case Assistant_Ds_WxMessageSendRecord::SEND_TYPE_VOICE_REPORT_GROUP:
                $this->groupCustom();
                break;
            case Assistant_Ds_WxMessageSendRecord::SEND_TYPE_DEER_CEPING_STAGE1_REPORT:
                $this->filterDeerCepingByTag();
                $reportMap = $this->getCepingReportMap(["student_uid", "is_deer_evaluate_stage1_report_generate", "deer_evaluate_stage1_participate_ti_cnt", "deer_evaluate_stage1_right_ti_cnt"]);
                $this->filterDeerCepingByReport($reportMap, "is_deer_evaluate_stage1_report_generate");
                $this->groupDeerCepingStage1($reportMap);
                break;
            case Assistant_Ds_WxMessageSendRecord::SEND_TYPE_DEER_CEPING_STAGE2_REPORT:
                $this->filterDeerCepingByTag();
                $reportMap = $this->getCepingReportMap(["student_uid", "is_deer_evaluate_stage2_report_generate", "deer_evaluate_stage2_report_grading"]);
                $this->filterDeerCepingByReport($reportMap, "is_deer_evaluate_stage2_report_generate");
                $this->groupDeerCepingStage2($reportMap);
                break;
            case Assistant_Ds_WxMessageSendRecord::SEND_TYPE_PERSONAL_LEARN_FEEDBACK:
                $this->groupDeskGo();
                break;
            default:
                throw new  Common_Exception(Common_ExceptionCodes::DESK_PARAM_ERROR, '暂不支持该发送场景');

        }
    }

    public function execute($arrInput) {
        try{
            //参数校验 & 初始化
            $this->initParam($arrInput);

            //统一筛选
            $this->studentList = $this->objSceneConf->getCommonStudentList($arrInput);

            //真人用户对学生别名标记
            $this->checkStudentNameLabels();
            $this->buildStudentWxInfo();

            Bd_Log::notice(sprintf("before sceneFilter, studentList: %s", json_encode($this->studentList)));
            //场景筛选
            $this->sceneFilter();

        }catch (Exception $e) {
            Bd_Log::warning($e->getMessage());
        }
        Bd_Log::notice(sprintf("[GroupFilter] 获取学生信息成功, list: %s", json_encode($this->output)));
        return $this->output;
    }

    /**
     * 预习点评
     * @throws Common_Exception
     */
    private function getPreview() {
        //获取测试类型
        $this->getExamType();
        $this->getStudentBasicInfo();
        //获取测试信息
        $this->getExamInfo();
        //小班名称
        $this->getClassInfo();
        //按照预习结果分组
        $this->formatOutput();
    }

    private function getReport() {
        $this->getStudentBasicInfo();
        $this->getReportInfo();
        $this->getClassInfo();
        $this->formatReportOutput();

    }

    const CUSTOM_GROUP_DEFAULT = "groupDefault";
    const CUSTOM_GROUP_PRONUNCIATION_LEVEL = "groupByPronunciationLevel";
    const CUSTOM_GROUP_PRONUNCIATION_ANSWER = "groupByPronunciationAnswer";

    const GROUP_CEPING_LEVEL = "groupCepingLevel";
    const GROUP_CEPING_TYPE = "groupCepingType";

    const  PronunciationLevel = [
        0 => "未提交",
        1 => "一星",
        2 => "二星",
        3 => "三星"
    ];

    const  AnswerResult = [
        0 => "错误",
        1 => "正确",
    ];
    const SCENE_EXAM_TYPE_FIELD = [
        AssistantDesk_ArkConfig::SEND_TYPE_PRONUNCIATION_REPORT_GROUP => "exam58",
        AssistantDesk_ArkConfig::SEND_TYPE_VOICE_REPORT_GROUP => "exam61"
    ];

    const GroupContentBtnMap=[
        AssistantDesk_ArkConfig::SEND_TYPE_PRONUNCIATION_REPORT_GROUP => ['纠音报告图片', '纠音报告卡片'],
        AssistantDesk_ArkConfig::SEND_TYPE_VOICE_REPORT_GROUP => ['配音小达人报告卡片']
    ];
    const GroupVariableNamesMap=[
        AssistantDesk_ArkConfig::SEND_TYPE_PRONUNCIATION_REPORT_GROUP => ['学生名', '章节名称','课程名称','上课时间','纠音报告链接','课程累计开口总次数'],
        AssistantDesk_ArkConfig::SEND_TYPE_VOICE_REPORT_GROUP => ['学生名', '章节名称','课程名称','上课时间','配音小达人报告链接'],
    ];

    private function groupCustom()
    {

        $items = json_decode($this->groupItem, true);
        if (empty($items['key'])) {
            $items['key'] = self::CUSTOM_GROUP_DEFAULT;
        }

        $this->getAttendTimeline();
        $this->getClassInfo();
        $this->getStudentBasicInfo();
        $this->getStudentLessonData();
        $this->getAttendSummaryImg();

        $this->output['attendTimeline'] = $this->arrTimeLine;
        $this->output['tips'] = $this->strTips;
        $this->output['unBindList'] = array_values($this->arrUnBindWxStudentList);
        $this->output['variableNames'] = self::GroupVariableNamesMap[$this->sendType];


        if (in_array("课程累计开口总次数",self::GroupVariableNamesMap[$this->sendType])){
            //要提前过滤掉没过开口的学员
            $studentUidList = array_column($this->arrBindWxStudentList, 'studentUid');
            $esCommonCuData = Assistant_Common_Service_DataService_Query_CourseStudentData::getCommonListByCourseIdStudentUids($this->courseId,$studentUidList, [$this->assistantUid], ['open_mouth_cnt','student_uid']);
            $esCourseDataMap=array_column($esCommonCuData, null, 'student_uid');
            $newArrBindWxStudentList=[];
            foreach ($this->arrBindWxStudentList as $student) {
                if ($esCourseDataMap[$student['studentUid']]['open_mouth_cnt'] > 0) {
                    $newArrBindWxStudentList[] = $student;
                }
            }
            $this->arrBindWxStudentList=$newArrBindWxStudentList;
        }

        if (!empty($this->arrBindWxStudentList)) {
            switch ($items['key']) {
                case self::CUSTOM_GROUP_DEFAULT:
                    $this->output['groupList'] = $this->groupCustomByDefault();
                break;
            case self::CUSTOM_GROUP_PRONUNCIATION_LEVEL:
                $this->output['groupList'] = $this->groupByPronunciationLevel();
                break;
            case self::CUSTOM_GROUP_PRONUNCIATION_ANSWER:
                $this->output['groupList'] = $this->groupByAnswerResult($items['param']);
                break;
            default:
                throw new  Common_Exception(Common_ExceptionCodes::DESK_PARAM_ERROR, '暂不支持该自定义分组');
            }
        }
    }

    /**
     * 根据报告状态过滤学员列表，移除符合条件的学员
     * @param array $reportMap 报告数据映射
     * @param string $checkField 检查字段名
     */
    private function filterDeerCepingByReport($reportMap, $checkField)
    {
        if (empty($reportMap)) {
            return;
        }

        $studentsToRemove = [];

        // 遍历当前学生列表
        foreach ($this->studentList as $studentUid => $studentInfo) {
            // 检查该学生是否在reportMap中存在，并且checkField字段值为1
            if (isset($reportMap[$studentUid]) && $reportMap[$studentUid][$checkField] == 1) {
                $studentsToRemove[] = $studentUid;
            }
        }

        // 从学生列表中移除符合条件的学生
        foreach ($studentsToRemove as $studentUid) {
            unset($this->studentList[$studentUid]);
        }
    }

    private function getCepingReportMap($fields)
    {
        $studentUids = array_keys($this->studentList);
        if (empty($studentUids)) {
            return [];
        }

        $reportData = Common_Singleton::getInstanceData(Api_DataProxy::class, "getLeadsDataByCourseStudents", [$studentUids, $this->courseId, $fields]);
        if ($reportData == false) {
            return [];
        }

        return array_column($reportData, null, 'student_uid');
    }

    /**
     * 处理进校测评的自定义筛选：自定义标签、运营标签等
     */
    private function filterDeerCepingByTag()
    {
        $sceneFilter = $this->sceneFilter;
        if (empty($sceneFilter)) {
            return;
        }

        // 获取所有学生的 UID
        $studentUids = array_keys($this->studentList);
        if (empty($studentUids)) {
            return;
        }

        try {
            $customTagService = new Service_Data_Desk_CustomTagCourseStudent();

            // 定义所有标签类型及其对应的参数
            $tagTypes = [
                Service_Data_Desk_CustomTagCourseStudent::TAG_TYPE_CUSTOM_TAG => [
                    'positive' => 'customTagNew',
                    'negative' => 'customTagNewConvertSelect',
                    'name' => '自定义标签'
                ],
                Service_Data_Desk_CustomTagCourseStudent::TAG_TYPE_OPERATOR_TAG => [
                    'positive' => 'operationTagNew',
                    'negative' => 'operationTagNewConvertSelect',
                    'name' => '运营标签'
                ]
            ];

            // 收集所有需要处理的标签类型和过滤条件
            $allFilterConditions = [];
            $tagTypesToProcess = [];

            foreach ($tagTypes as $tagType => $config) {
                $positiveParam = $config['positive'];
                $negativeParam = $config['negative'];
                $tagName = $config['name'];

                // 检查是否有该类型的过滤条件
                if (!empty($sceneFilter[$positiveParam]) || !empty($sceneFilter[$negativeParam])) {
                    Bd_Log::notice("filterDeerCepingByTag 发现{$tagName}过滤条件");

                    // 添加到需要处理的标签类型列表
                    $tagTypesToProcess[] = $tagType;

                    // 添加过滤条件
                    if (!empty($sceneFilter[$positiveParam])) {
                        $allFilterConditions[$tagType][] = [
                            'tagList' => $sceneFilter[$positiveParam],
                            'isPositive' => true,
                            'paramName' => $positiveParam
                        ];
                    }

                    if (!empty($sceneFilter[$negativeParam])) {
                        $allFilterConditions[$tagType][] = [
                            'tagList' => $sceneFilter[$negativeParam],
                            'isPositive' => false,
                            'paramName' => $negativeParam
                        ];
                    }
                }
            }

            // 如果没有任何过滤条件，直接返回
            if (empty($tagTypesToProcess)) {
                Bd_Log::notice("filterDeerCepingByTag 没有任何标签过滤条件，跳过");
                return;
            }

            // 一次性获取所有需要的标签数据
            $allStudentTagsMap = [];
            foreach ($tagTypesToProcess as $tagType) {
                // 获取学生标签信息
                $studentTagsList = $customTagService->getStudentTagsListFromCacheByCUT(
                    $this->courseId,
                    $studentUids,
                    $tagType
                );

                // 将学生标签列表转换为以studentUid为键的映射数组
                $studentTagsMap = [];
                foreach ($studentTagsList as $tagInfo) {
                    $studentTagsMap[$tagInfo['studentUid']] = $tagInfo['tagsArr'];
                }

                $allStudentTagsMap[$tagType] = $studentTagsMap;
                Bd_Log::notice("filterDeerCepingByTag 获取到标签类型{$tagType}的学生标签信息: " . json_encode($studentTagsMap));
            }

            // 应用所有过滤条件
            $this->applyAllTagFilters($allStudentTagsMap, $allFilterConditions);

        } catch (Exception $e) {
            Bd_Log::warning("filterDeerCepingByTag 过滤学生失败: " . $e->getMessage());
        }
    }

    /**
     * 应用所有标签类型的过滤条件
     *
     * @param array $allStudentTagsMap 所有标签类型的学生标签映射
     * @param array $allFilterConditions 所有标签类型的过滤条件
     */
    private function applyAllTagFilters($allStudentTagsMap, $allFilterConditions)
    {
        $studentsToRemove = [];

        // 遍历学生列表
        foreach ($this->studentList as $studentUid => $studentInfo) {
            // 遍历每种标签类型
            foreach ($allFilterConditions as $tagType => $conditions) {
                $studentTags = $allStudentTagsMap[$tagType][$studentUid] ?? [];

                // 应用该类型的所有过滤条件
                foreach ($conditions as $condition) {
                    $tagList = $condition['tagList'];
                    $isPositive = $condition['isPositive'];
                    $paramName = $condition['paramName'];

                    // 检查学生是否拥有任一指定标签
                    $hasTag = false;
                    foreach ($studentTags as $tag) {
                        if (in_array($tag['tag'], $tagList)) {
                            $hasTag = true;
                            break;
                        }
                    }

                    // 根据是否是正选标签和学生是否拥有标签，决定是否将学生添加到移除列表
                    if (($isPositive && !$hasTag) || (!$isPositive && $hasTag)) {
                        $studentsToRemove[$studentUid] = true;
                        break 2; // 对于已经要移除的学生，跳出两层循环，不再检查其他标签类型
                    }
                }
            }
        }

        // 从学生列表中移除不符合条件的学生
        foreach (array_keys($studentsToRemove) as $studentUid) {
            unset($this->studentList[$studentUid]);
        }
    }

    /**
     * 处理一段报告分组
     */
    private function groupDeerCepingStage1($reportMap)
    {
        $items = json_decode($this->groupItem, true);
        if (empty($items['key'])) {
            $items['key'] = self::CUSTOM_GROUP_DEFAULT;
        }

        $this->getClassInfo();
        $this->getStudentBasicInfo();

        $this->output['unBindList'] = array_values($this->arrUnBindWxStudentList);
        $this->output['variableNames'] = ['课程名称', '学生名', '辅导老师真名'];

        if (!empty($this->arrBindWxStudentList)) {
            switch ($items['key']) {
                // 不分组
                case self::CUSTOM_GROUP_DEFAULT:
                    $this->output['groupList'] = self::groupDeerCepingStage1Default($reportMap);
                    break;
                // 按类别分组
                case self::GROUP_CEPING_TYPE:
                    $this->output['groupList'] = self::groupDeerCepingStage1Level($reportMap);
                    break;
                default:
                    throw new Common_Exception(Common_ExceptionCodes::DESK_PARAM_ERROR, '暂不支持该自定义分组');
            }
        } else {
            Bd_Log::notice("groupDeerCepingStage1 没有可绑定微信的学生，不进行分组");
        }
    }

    /**
     * 处理二段报告分组
     */
    private function groupDeerCepingStage2($reportMap)
    {
        $items = json_decode($this->groupItem, true);
        if (empty($items['key'])) {
            $items['key'] = self::CUSTOM_GROUP_DEFAULT;
        }

        $this->getClassInfo();
        $this->getStudentBasicInfo();

        $this->output['unBindList'] = array_values($this->arrUnBindWxStudentList);
        $this->output['variableNames'] = ['课程名称', '学生名', '辅导老师真名'];

        if (!empty($this->arrBindWxStudentList)) {
            switch ($items['key']) {
                // 不分组
                case self::CUSTOM_GROUP_DEFAULT:
                    $this->output['groupList'] = self::groupDeerCepingStage2Default($reportMap);
                    break;
                // 按评级分组
                case self::GROUP_CEPING_LEVEL:
                    $this->output['groupList'] = self::groupDeerCepingStage2Level($reportMap);
                    break;
                default:
                    throw new  Common_Exception(Common_ExceptionCodes::DESK_PARAM_ERROR, '暂不支持该自定义分组');
            }
        }
    }

    private function groupDeerCepingStage1Default($reportMap)
    {
        // 定义分组配置 - 只有一个分组（不分组）
        $groupConfigs = [
            [
                'id' => 'all_users',
                'title' => '不分组',
                'condition' => function($reportData) {
                    // 所有学生都满足条件
                    return true;
                },
                'defaultTpl' => [''],
                'labelList' => [],
                'contentBtnList' => [],
            ],
        ];

        return $this->groupCepingByConfig($groupConfigs, $reportMap);
    }

    private function groupDeerCepingStage1Level($reportMap)
    {
        // 定义分组配置
        $groupConfigs = [
            [
                'id' => 'good_users',
                'title' => '抽象能力强',
                'condition' => function($reportData) {
                    if ($reportData['deer_evaluate_stage1_right_ti_cnt']/$reportData['deer_evaluate_stage1_participate_ti_cnt'] >= 0.65) {
                        return true;
                    }
                    return false;
                },
                'defaultTpl' => [''],
                'labelList' => [],
                'contentBtnList' => [],
            ],
            [
                'id' => 'qualified_users_yiduan',
                'title' => '抽象能力弱',
                'condition' => function($reportData) {
                    if ($reportData['deer_evaluate_stage1_right_ti_cnt']/$reportData['deer_evaluate_stage1_participate_ti_cnt'] < 0.65) {
                        return true;
                    }
                    return false;
                },
                'defaultTpl' => [''],
                'labelList' => [],
                'contentBtnList' => [],
            ],
            // 可以根据需要添加更多分组
        ];

        return $this->groupCepingByConfig($groupConfigs, $reportMap);
    }

    private function groupDeerCepingStage2Default($reportMap)
    {
        // 定义分组配置 - 只有一个分组（不分组）
        $groupConfigs = [
            [
                'id' => 'all_users',
                'title' => '不分组',
                'condition' => function($reportData) {
                    // 所有学生都满足条件
                    return true;
                },
                'defaultTpl' => [''],
                'labelList' => [],
                'contentBtnList' => ['二段报告卡片'],
                'selectedContentBtnList' => ['二段报告卡片'],
            ],
        ];

        return $this->groupCepingByConfig($groupConfigs, $reportMap);
    }

    private function groupDeerCepingStage2Level($reportMap)
    {
        // 定义分组配置
        $groupConfigs = [
            [
                'id' => 'good_users_erduan',
                'title' => '🏆全能选手',
                'condition' => function($reportData) {
                    if ($reportData['deer_evaluate_stage2_report_grading'] == 1) {
                        return true;
                    }
                    return false;
                },
                'defaultTpl' => [''],
                'labelList' => [],
                'contentBtnList' => ['二段报告卡片'],
                'selectedContentBtnList' => ['二段报告卡片'],
            ],
            [
                'id' => 'thinker_users_erduan',
                'title' => '🎖️潜力之星',
                'condition' => function($reportData) {
                    if ($reportData['deer_evaluate_stage2_report_grading'] == 2) {
                        return true;
                    }
                    return false;
                },
                'defaultTpl' => [''],
                'labelList' => [],
                'contentBtnList' => ['二段报告卡片'],
                'selectedContentBtnList' => ['二段报告卡片'],
            ],
            [
                'id' => 'practical_users_erduan',
                'title' => '👨‍🎓思考家',
                'condition' => function($reportData) {
                    if ($reportData['deer_evaluate_stage2_report_grading'] == 3) {
                        return true;
                    }
                    return false;
                },
                'defaultTpl' => [''],
                'labelList' => [],
                'contentBtnList' => ['二段报告卡片'],
                'selectedContentBtnList' => ['二段报告卡片'],
            ],
            [
                'id' => 'potential_users_erduan',
                'title' => '🧗🏻‍♂️实践家 ',
                'condition' => function($reportData) {
                    if ($reportData['deer_evaluate_stage2_report_grading'] == 4) {
                        return true;
                    }
                    return false;
                },
                'defaultTpl' => [''],
                'labelList' => [],
                'contentBtnList' => ['二段报告卡片'],
                'selectedContentBtnList' => ['二段报告卡片'],
            ],
        ];

        return $this->groupCepingByConfig($groupConfigs, $reportMap);
    }

    /**
     * 通用的分组处理方法，根据传入的分组配置进行分组
     * @param array $groupConfigs 分组配置
     * @param array $reportMap 报告数据映射
     * @return array 分组结果
     */
    private function groupCepingByConfig($groupConfigs, $reportMap)
    {
        if (empty($reportMap)) {
            return [];
        }

        // 初始化所有分组
        $groups = [];
        foreach ($groupConfigs as $config) {
            $groups[$config['id']] = [
                'title' => $config['title'],
                'labelList' => $config['labelList'],
                'bindList' => [],
                'defaultTpl' => $config['defaultTpl'],
                'contentBtnList' => $config['contentBtnList'],
                'selectedContentBtnList' => $config['selectedContentBtnList'],
            ];
        }

        // 按小班分组的临时存储
        $arrGroupByClassIdMap = [];
        foreach ($groupConfigs as $config) {
            $arrGroupByClassIdMap[$config['id']] = [];
        }

        // 遍历学生并分配到相应分组
        foreach ($this->arrBindWxStudentList as $student) {
            // 学生报告数据
            $reportData = $reportMap[$student['studentUid']];

            // 根据条件将学生分配到相应分组
            foreach ($groupConfigs as $config) {
                if ($config['condition']($reportData)) {
                    $classId = $student['classId'];
                    $groupId = $config['id'];

                    if (!isset($arrGroupByClassIdMap[$groupId][$classId])) {
                        $arrGroupByClassIdMap[$groupId][$classId] = [
                            'classId' => $classId,
                            'className' => isset($this->arrClassId2Name[$classId]) ? $this->arrClassId2Name[$classId] : '',
                            'studentList' => [],
                        ];
                    }

                    $arrGroupByClassIdMap[$groupId][$classId]['studentList'][] = $student;
                }
            }
        }

        // 将分组后的数据填充到最终结果中，并设置gcid
        $_index = 0;
        foreach ($groupConfigs as $config) {
            $groupId = $config['id'];
            if (!empty($arrGroupByClassIdMap[$groupId])) {
                foreach ($arrGroupByClassIdMap[$groupId] as $classId => &$classGroup) {
                    $classGroup['gcid'] = "idx_{$_index}_$classId";
                }
                $groups[$groupId]['bindList'] = array_values($arrGroupByClassIdMap[$groupId]);
                $_index++; // 只在处理完一个分组后递增
            }
        }

        // 过滤掉bindList为空的分组
        $filteredGroups = [];
        foreach ($groups as $groupId => $group) {
            if (!empty($group['bindList'])) {
                $filteredGroups[$groupId] = $group;
            }
        }

        // 返回所有分组的数组
        return array_values($filteredGroups);
    }

    private function groupCustomByDefault()
    {
        $arrRet = [];
        $arrRet['title'] = '默认分组';
        $arrRet['labelList'] = [];

        $studentUidList = array_column($this->arrBindWxStudentList, 'studentUid');
        $esLessonData = Common_Singleton::getInstanceData(Api_DataProxy::class, 'getCommonLuByLessonStudents', [$this->lessonId, $studentUidList, ['student_uid', 'exam58', 'exam61']]);

        if ($esLessonData == false) {
            $esLessonData = [];
        }
        $esLessonDataMap = array_column($esLessonData, null, 'student_uid');
        // 按小班分组
        $arrGroupByClassId = [];
        $_index = 0;
        foreach ($this->arrBindWxStudentList as $student) {
            $examInfo = $esLessonDataMap[$student['studentUid']][self::SCENE_EXAM_TYPE_FIELD[$this->sendType]];
            if (empty($examInfo)){
                continue;
            }
            $isSubmit = json_decode($examInfo, true)['is_submit'];
            if(!$isSubmit){
                continue;
            }

            $classId = $student['classId'];
            if (!isset($arrGroupByClassId[$classId])) {
                $arrGroupByClassId[$classId] = [
                    'classId' => $classId,
                    'className' => isset($this->arrClassId2Name[$classId]) ? $this->arrClassId2Name[$classId] : '',
                    'studentList' => [],
                ];
            }
            $arrGroupByClassId[$classId]['studentList'][] = $student;
            $arrGroupByClassId[$classId]['gcid'] = "idx_{$_index}_$classId";
        }
        $arrRet['bindList'] = array_values($arrGroupByClassId);
        $arrRet['contentBtnList'] = self::GroupContentBtnMap[$this->sendType];

        $arrRet['defaultTpl'] = [''];
        return [$arrRet];
    }

    private function groupByPronunciationLevel()
    {
        $arrRet = [];

        $studentUidList = array_column($this->arrBindWxStudentList, 'studentUid');
        $esLessonData = Common_Singleton::getInstanceData(Api_DataProxy::class, 'getCommonLuByLessonStudents', [$this->lessonId, $studentUidList, ['student_uid', 'exam58', 'exam61']]);

        if ($esLessonData == false) {
            $esLessonData = [];
        }
        $esLessonDataMap = array_column($esLessonData, null, 'student_uid');

        $groupStudent = [];
        foreach ($this->arrBindWxStudentList as $student) {
            $groupName = "未提交";
            $examInfo = $esLessonDataMap[$student['studentUid']][self::SCENE_EXAM_TYPE_FIELD[$this->sendType]];
            if ($examInfo) {
                if ($this->sendType==Assistant_Ds_WxMessageSendRecord::SEND_TYPE_PRONUNCIATION_REPORT_GROUP){
                    $score = floatval(json_decode($examInfo, true)["pronunciation_score"]);
                    if ($score > 70) {
                        $groupName = '三星';
                    } else if ($score > 50) {
                        $groupName = '二星';
                    } else {
                        $groupName = '一星';
                    }
                }else {
                    $groupName = self::PronunciationLevel[json_decode($examInfo, true)["pronunciation_star"]] ?? "未提交";
                }
            }
            $groupStudent[$groupName][] = $student;
        }
        // 按小班分组
        $index = 0;
        foreach ($groupStudent as $groupName => $studentList) {
            if ($groupName == "未提交"){
                Bd_Log::notice("groupByPronunciationLevel unSubmit student:".json_encode($studentList));
                continue;
            }
            $arrGroupByClassId = [];
            foreach ($studentList as $student) {
                $classId = $student['classId'];
                if (!isset($arrGroupByClassId[$classId])) {
                    $arrGroupByClassId[$classId] = [
                        'classId' => $classId,
                        'className' => isset($this->arrClassId2Name[$classId]) ? $this->arrClassId2Name[$classId] : '',
                        'studentList' => [],
                    ];

                }
                $arrGroupByClassId[$classId]['studentList'][] = $student;
            }
            $arrGroupByClassIdValueList = [];
            foreach (array_values($arrGroupByClassId) as $arrGroupByClassIdValue){
                $tmpClassId = $arrGroupByClassIdValue['classId'];
                $arrGroupByClassIdValue['gcid']="idx_{$index}_$tmpClassId";
                $arrGroupByClassIdValue['studentList'][]=
                $arrGroupByClassIdValueList[]=$arrGroupByClassIdValue;
            }
            $arrRet[] = [
                'bindList' => array_values($arrGroupByClassIdValueList),
                'defaultTpl' => [''],
                'contentBtnList' => self::GroupContentBtnMap[$this->sendType],
                'title' => $groupName,
                "labelList" => []
            ];
            $index++;
        }

        return $arrRet;
    }

    /**
     * 根据年级获取考试类型
     * @throws Common_Exception
     */
    private function getExamType() {

        if (!$this->mainGradeId) {
            throw new Common_Exception(Common_ExceptionCodes::DESK_PARAM_ERROR, '场景化群发，获取考试类型失败');
        }
        $this->examType = Api_Exam::BIND_TYPE_POSTTEST_MORE;
        if(Zb_Const_GradeSubject::$GRADEMAPXB[$this->mainGradeId] == Zb_Const_GradeSubject::GRADE_STAGE_PRIMARY
            ||  Zb_Const_GradeSubject::$GRADEMAPXB[$this->mainGradeId] == Zb_Const_GradeSubject::GRADE_STAGE_PRESCHOOL){
            $this->examType = Api_Exam::BIND_TYPE_PREVIEW;
        }
    }

    /**
     * 获取试卷相关信息
     * @throws Common_Exception
     */
    private function getExamInfo(){

        //根据绑定关系获取到当前存在的所有绑定examId
        $bindList = "lesson_" . $this->lessonId. ":" . $this->examType;
        $examRelationList = Api_Examcore::getRelation([$bindList]);
        $examRelationList = $examRelationList ?? [];
        $examIds = $examRelationList ? array_unique(array_column($examRelationList[$bindList], 'examId')) : [];
        $examId = intval($examIds[0]);
        if(!$examId){
            throw new Common_Exception(Common_ExceptionCodes::DESK_NETWORK_ERROR, '未获取到绑定试卷');
        }

        //根据examId获取数据信息和作答信息
        $examData = Api_Examcore::getExam([$examId], true);
        if(false === $examData){
            throw new Common_Exception(Common_ExceptionCodes::DESK_NETWORK_ERROR, '未获取到有效试卷');
        }

        $examData = $examData[$examId];
        // 按sort字段给题目排序
        $arrSort = [];
        foreach ($examData['exam']['tidList'] as $tid => $v) {
            $arrSort[$v['sort']] = $tid;
        }
        ksort($arrSort);
        $arrTmp = [];
        foreach ($arrSort as $k => $tid) {
            if (isset($examData['questionList'][$tid])) {
                $arrTmp[$tid] = $examData['questionList'][$tid];
            }
        }

        $examInfo = [
            'examInfo'      => $examData['exam'],
            'questionList'  => $arrTmp,
        ];

        //获取当前examId、学生UIDS对应的作答数据
        $answerKeys = [];
        foreach ($this->studentList as $studentUid => $items){
            $answerKeys[] = $examId."_".$studentUid;
        }
        $answerData = Api_Examcore::getAnswer($answerKeys);
        $answerList = [];
        foreach ($answerData as $val){
            $answerList[intval($val[0]['studentUid'])] = $val[0];
        }

        $this->examInfo   = $examInfo;
        $this->answerList = $answerList;
    }


    /**
     * 拼装默认发送文案
     * @param $answerKey
     * @return array
     * @throws Common_Exception
     */
    private function getTemplate($answerKey){
        $answers = explode('_', $answerKey);

        $tids = $this->examInfo['questionList'] ? array_keys($this->examInfo['questionList']) : [];
        if(empty($tids)){
            throw new Common_Exception(Common_ExceptionCodes::DESK_NETWORK_ERROR, '获取预习题目异常');
        }

        //获取需要匹配的文案---小学、初高不一样
        if(Api_Exam::BIND_TYPE_PREVIEW == $this->examType){
            $previewTidInfoPath  = "/home/<USER>/app/" . MAIN_APP . "/library/config/yx_xiaoxue_yuxi_tid_analysis.json";
//            $previewDiffInfoPath = "/home/<USER>/app/assistantdesk/library/config/yx_xiaoxue_yuxi_diffcult.json";
        }else{
            $previewTidInfoPath  = "/home/<USER>/app/" . MAIN_APP . "/library/config/yx_chuzhong_yuxi_tid_analysis.json";
//            $previewDiffInfoPath = "/home/<USER>/app/assistantdesk/library/config/yx_chuzhong_yuxi_diffcult.json";
        }
        $previewTidInfoConfig   = file_get_contents($previewTidInfoPath);
//        $previewDiffInfoConfig  = file_get_contents($previewDiffInfoPath);
        $previewTidInfoConfig   = json_decode($previewTidInfoConfig, true);
//        $previewDiffInfoConfig  = json_decode($previewDiffInfoConfig, true);

        //获取cpuID
        $cpuId = intval($this->courseInfo['cpuId']);

        //获取大纲ID
        $outlineId  = intval($this->courseInfo['lessonList'][$this->lessonId]['outlineId']);

        //获取主讲老师信息
        $teacherInfos = AssistantDesk_Teacher::getTeacherNameByCourseIds([$this->courseId]);

        if(0 >= $cpuId || 0 >= $outlineId){
            return "";
        }

        $cnt = count($tids);
        $rightCnt =0;
        $errList = [];
        foreach ($tids as $key => $tid){
            $rightCnt = $answers[$key] ? $rightCnt + 1 : $rightCnt;
            if(!$answers[$key]){
                $errList[] = $key + 1;
            }
        }

        $content = "你好，老师已经收到了你今天的预习，先表扬一下~";
        $content .= "本次预习一共{$cnt}题，你做对了{$rightCnt}题，";

        $rightRank  = $cnt ? intval($rightCnt * 100 / $cnt) : 0;
        if($rightRank < 33){
            $content .= "这次做的不太理想。\n";
        }else if($rightRank < 66){
            $content .= "有几个细节没太注意，错了一些题目。\n";
        }else{
            $content .= "非常棒，预习部分内容掌握得不错。\n";
        }

        if($errList){
            $content .= "其中第".implode('、', $errList)."题你需要重点关注，下面我来给你分析一下，仔细看哦。\n";
        }

        foreach ($tids as $key => $tid){
            /*if($answers[$key]){
                continue;
            }*/
            $rightOrErrKey = $answers[$key] ? 'right' : 'error';
            $tidInfo = $previewTidInfoConfig[$cpuId][$outlineId][$tid][$rightOrErrKey] ? $previewTidInfoConfig[$cpuId][$outlineId][$tid][$rightOrErrKey] : "";
            if(empty($tidInfo)){
                continue;
            }
            $content .= $tidInfo."\n";
        }

        $content .= "这一讲的预习已经结束了，我们将会在".date('Y-m-d H:i:s', $this->courseInfo['lessonList'][$this->lessonId]['startTime'])."讲解这部分内容。别忘了来上课哦~";

        return ['#学生名#' ,$content];
    }

    /**
     * @param $studentUidList
     * @return array
     */
    private function formatStudentList($studentUidList, $index,$useLabel=0){
        $studentInfos = Api_Dau::getStudents(array_keys($this->studentList), ['studentUid', 'studentName', 'phone', 'guardianPhone', 'fatherPhone', 'motherPhone', 'registerPhone', 'uname']);
        $classGroup = [];
        foreach ($studentUidList as $studentUid){
            $classId    = $this->studentList[$studentUid]['classId'];

            $row['classId']        =  $classId;
            $row['studentUid']     =  $studentUid;
            $row['studentName']    =  $this->studentList[$studentUid]['studentName'];
            $row['phone']          =  $this->studentList[$studentUid]['phone'];
            $row['wxIdsList']      =  $this->studentUidToWxIds[$studentUid] ?? [];
            // $row['wxNum']          =  $this->studentList[$studentUid]['wxNum'];
            if ($this->reportMap[$studentUid]){
                $row['picUrl'] = Service_Data_Sport_SportWeekReport::getReportUrl($this->reportMap[$studentUid]['report_id']);
            }
            if ($useLabel == 1) {
                $row['useLabel'] = Service_Data_User_UserBindConfig::checkStudentUseNameLabels($studentInfos[$studentUid]['studentName'], $studentInfos[$studentUid]['uname']);
                //为了兼容前端，搞了这个东西
                $row['attendSummary'] = [

                    'ori' => 'sport_week_sample.png',
                    'name' => 'zyb_e0e4e49424149a6d30b22e80a63f1ed8.jpg',
                    'url' => 'https://img.zuoyebang.cc/zyb_e0e4e49424149a6d30b22e80a63f1ed8.jpg',
                    'width' => 720,
                    'height' => 624,
                ];
            }

            //按照小班分组
            $classGroup[$classId]['classId']       =  $classId;
            $classGroup[$classId]['gcid']          =  "idx_{$index}_$classId";
            $classGroup[$classId]['className']     =  $this->arrClassId2Name[$classId];
            $classGroup[$classId]['studentList'][] =  $row;
        }
        return array_values($classGroup);
    }

        /**
         * @param $param
         * @return array
         * @throws Common_Exception
         */
        public function groupByAnswerResult($param)
        {
            $arrRet = [];
            $studentUidList = array_column($this->arrBindWxStudentList, 'studentUid');
            $idx = intval($param); //获取题号
            if ($idx <= 0) {
                $this->output['tips'] = '题号不正确';
                throw new  Common_Exception(Common_ExceptionCodes::DESK_PARAM_ERROR, $this->output['tips']);
            }

            $hwBindExams = AssistantDesk_ExamBind::lessonBindExams(
                [$this->lessonId],
                [Api_Exam::BIND_TYPE_EXAM_TYPE_PRONUNCIATION]
            );
            Bd_Log::notice("groupByAnswerResult bindInfo,param=".json_encode($param),"response:".json_encode($hwBindExams));

            $examInfoMap = $hwBindExams[$this->lessonId][Api_Exam::BIND_TYPE_EXAM_TYPE_PRONUNCIATION];
            if (empty($examInfoMap)) {
                $this->output['tips'] = '未绑定纠音试卷';
                throw new  Common_Exception(Common_ExceptionCodes::DESK_PARAM_ERROR, $this->output['tips']);
            }
            $examId = 0;
            foreach($examInfoMap as $key =>$examInfo){
                $examId = $key;
            }
            if ($examId == 0) {
                $this->output['tips'] = '未绑定纠音试卷';
                throw new  Common_Exception(Common_ExceptionCodes::DESK_PARAM_ERROR, $this->output['tips']);
            }


            $tid = 0;
            //查询提信息
            $examInfoDetail = Api_Examcore::getExamInfo([$examId], 1);
            if (empty($examInfoDetail) || empty($examInfoDetail[$examId])) {
                $this->output['tips'] = '试卷不存在';
                throw new  Common_Exception(Common_ExceptionCodes::DESK_PARAM_ERROR, $this->output['tips']);
            }
            if (empty($examInfoDetail[$examId]['exam']['tidList'])) {
                $this->output['tips'] = '试卷小题不存在';
                throw new  Common_Exception(Common_ExceptionCodes::DESK_PARAM_ERROR, $this->output['tips']);
            }
            foreach ($examInfoDetail[$examId]['exam']['tidList'] as $_id => $tidDetail) {
                if ($tidDetail['sort'] == $idx) {
                    $tid = $_id;
                }
            }
            if ($tid <= 0) {
                $this->output['tips'] = '题号不存在';
                throw new  Common_Exception(Common_ExceptionCodes::DESK_PARAM_ERROR, $this->output['tips']);
            }

            //查询数据组学生作答信息

            $answerData = Api_DataProxy::getLessonExamTidStudentUidList($this->lessonId, $examId, $tid, 0, $studentUidList, ['tid', 'correct_result', 'student_uid']);
            $answerDataMap = array_column($answerData, null, 'student_uid');
            $groupStudent = [];
            foreach ($this->arrBindWxStudentList as $student) {
                $groupName= "错误";
                $studentAnswer = $answerDataMap[$student['studentUid']];
                if ($studentAnswer) {
                    $groupName = self::AnswerResult[$studentAnswer['correct_result']] ?? "错误";
                }
                $groupStudent[$groupName][] = $student;
            }
            $index = 0;
            foreach ($groupStudent as $groupName => $studentList) {
                if ($groupName == "未提交"){
                    Bd_Log::notice("groupByAnswerResult unSubmit student:".json_encode($studentList));
                    continue;
                }
                $arrGroupByClassId = [];
                foreach ($studentList as $student) {
                    $classId = $student['classId'];
                    if (!isset($arrGroupByClassId[$classId])) {
                        $arrGroupByClassId[$classId] = [
                            'classId' => $classId,
                            'className' => isset($this->arrClassId2Name[$classId]) ? $this->arrClassId2Name[$classId] : '',
                            'studentList' => [],
                        ];
                    }
                    $arrGroupByClassId[$classId]['studentList'][] = $student;
                }
                $arrGroupByClassIdValueList = [];

                foreach (array_values($arrGroupByClassId) as $arrGroupByClassIdValue){
                    $tmpClassId = $arrGroupByClassIdValue['classId'];
                    $arrGroupByClassIdValue['gcid']="idx_{$index}_$tmpClassId";
                    $arrGroupByClassIdValueList[]=$arrGroupByClassIdValue;
                }

                $arrRet[] = [
                    'bindList' => array_values($arrGroupByClassIdValueList),
                    'defaultTpl' => [''],
                    'title' => $groupName,
                    'contentBtnList' => self::GroupContentBtnMap[$this->sendType],
                    "labelList" => []
                ];
                $index++;
            }
            return $arrRet;
    }

    private function initDimLpcLessonCommon() {
        if (empty($this->lessonId)) {
            return;
        }
        $dimLpcLessonCommon                   = Assistant_Common_Service_DataService_Query_LessonData::getListByLessonIds([$this->lessonId], ['lesson_id', 'mix_interaction_total_num']);
        $this->dimLpcLessonCommonWithLessonId = $dimLpcLessonCommon ? array_column($dimLpcLessonCommon, null, 'lesson_id') : [];
    }
}
